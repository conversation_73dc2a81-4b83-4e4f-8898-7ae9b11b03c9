import 'package:flutter/material.dart';

class AppProvider extends ChangeNotifier {
  // User Authentication State
  bool _isLoggedIn = false;
  Map<String, dynamic>? _user;
  
  // Wallet State
  double _walletBalance = 0.0;
  double _upgradeWallet = 0.0;
  double _withdrawalWallet = 0.0;
  
  // Earnings State
  double _dailyEarnings = 0.0;
  double _totalEarnings = 0.0;
  double _referralEarnings = 0.0;
  double _levelIncome = 0.0;
  
  // MLM State
  int _totalReferrals = 0;
  int _activeReferrals = 0;
  List<Map<String, dynamic>> _teamMembers = [];
  
  // Gaming State
  int _gamesPlayed = 0;
  double _gameEarnings = 0.0;
  bool _canPlayGame = true;
  
  // Video Watching State
  bool _hasWatchedIntroVideo = false;
  int _adsWatchedToday = 0;
  double _adEarnings = 0.0;
  bool _canWatchAds = true;
  
  // E-commerce State
  List<Map<String, dynamic>> _cartItems = [];
  List<Map<String, dynamic>> _orders = [];
  List<Map<String, dynamic>> _wishlist = [];
  
  // Investment Packages
  List<Map<String, dynamic>> _investmentPackages = [
    {
      'id': 1,
      'name': 'Starter Package',
      'amount': 500,
      'dailyIncome': 50,
      'validity': 30,
      'gameBonus': 200,
      'description': 'Perfect for beginners',
    },
    {
      'id': 2,
      'name': 'Premium Package',
      'amount': 2000,
      'dailyIncome': 200,
      'validity': 30,
      'gameBonus': 400,
      'description': 'Best value package',
    },
    {
      'id': 3,
      'name': 'VIP Package',
      'amount': 5000,
      'dailyIncome': 500,
      'validity': 30,
      'gameBonus': 600,
      'description': 'Maximum earnings',
    },
    {
      'id': 4,
      'name': 'Elite Package',
      'amount': 10000,
      'dailyIncome': 1000,
      'validity': 30,
      'gameBonus': 1000,
      'description': 'Elite level benefits',
    },
  ];
  
  // Current Package
  Map<String, dynamic>? _currentPackage;
  
  // Getters
  bool get isLoggedIn => _isLoggedIn;
  Map<String, dynamic>? get user => _user;
  double get walletBalance => _walletBalance;
  double get upgradeWallet => _upgradeWallet;
  double get withdrawalWallet => _withdrawalWallet;
  double get dailyEarnings => _dailyEarnings;
  double get totalEarnings => _totalEarnings;
  double get referralEarnings => _referralEarnings;
  double get levelIncome => _levelIncome;
  int get totalReferrals => _totalReferrals;
  int get activeReferrals => _activeReferrals;
  List<Map<String, dynamic>> get teamMembers => _teamMembers;
  int get gamesPlayed => _gamesPlayed;
  double get gameEarnings => _gameEarnings;
  bool get canPlayGame => _canPlayGame;
  bool get hasWatchedIntroVideo => _hasWatchedIntroVideo;
  int get adsWatchedToday => _adsWatchedToday;
  double get adEarnings => _adEarnings;
  bool get canWatchAds => _canWatchAds;
  List<Map<String, dynamic>> get cartItems => _cartItems;
  List<Map<String, dynamic>> get orders => _orders;
  List<Map<String, dynamic>> get wishlist => _wishlist;
  List<Map<String, dynamic>> get investmentPackages => _investmentPackages;
  Map<String, dynamic>? get currentPackage => _currentPackage;
  
  // Authentication Methods
  void login(Map<String, dynamic> userData) {
    _isLoggedIn = true;
    _user = userData;
    notifyListeners();
  }
  
  void logout() {
    _isLoggedIn = false;
    _user = null;
    notifyListeners();
  }
  
  // Wallet Methods
  void updateWalletBalance(double amount) {
    _walletBalance = amount;
    notifyListeners();
  }
  
  void addToUpgradeWallet(double amount) {
    _upgradeWallet += amount;
    notifyListeners();
  }
  
  void addToWithdrawalWallet(double amount) {
    _withdrawalWallet += amount;
    notifyListeners();
  }
  
  // Earnings Methods
  void addDailyEarnings(double amount) {
    _dailyEarnings += amount;
    _totalEarnings += amount;
    notifyListeners();
  }
  
  void addReferralEarnings(double amount) {
    _referralEarnings += amount;
    _totalEarnings += amount;
    notifyListeners();
  }
  
  void addLevelIncome(double amount) {
    _levelIncome += amount;
    _totalEarnings += amount;
    notifyListeners();
  }
  
  // MLM Methods
  void addReferral(Map<String, dynamic> referral) {
    _totalReferrals++;
    _teamMembers.add(referral);
    notifyListeners();
  }
  
  // Gaming Methods
  void playGame() {
    if (_canPlayGame && _currentPackage != null) {
      _gamesPlayed++;
      double bonus = _currentPackage!['gameBonus'].toDouble();
      _gameEarnings += bonus;
      addDailyEarnings(bonus);
      _canPlayGame = false; // Implement cooldown logic
      notifyListeners();
    }
  }
  
  void resetGameCooldown() {
    _canPlayGame = true;
    notifyListeners();
  }
  
  // Video Watching Methods
  void watchIntroVideo() {
    if (!_hasWatchedIntroVideo) {
      _hasWatchedIntroVideo = true;
      addDailyEarnings(100);
      notifyListeners();
    }
  }
  
  void watchAd() {
    if (_canWatchAds && _adsWatchedToday < 50) {
      _adsWatchedToday++;
      _adEarnings += 2; // ₹100/50 ads = ₹2 per ad
      addDailyEarnings(2);
      
      if (_adsWatchedToday >= 50) {
        _canWatchAds = false;
      }
      notifyListeners();
    }
  }
  
  void resetDailyAds() {
    _adsWatchedToday = 0;
    _canWatchAds = true;
    notifyListeners();
  }
  
  // E-commerce Methods
  void addToCart(Map<String, dynamic> product) {
    _cartItems.add(product);
    notifyListeners();
  }
  
  void removeFromCart(int index) {
    _cartItems.removeAt(index);
    notifyListeners();
  }
  
  void addToWishlist(Map<String, dynamic> product) {
    _wishlist.add(product);
    notifyListeners();
  }
  
  void removeFromWishlist(int index) {
    _wishlist.removeAt(index);
    notifyListeners();
  }
  
  void placeOrder(Map<String, dynamic> order) {
    _orders.add(order);
    _cartItems.clear();
    notifyListeners();
  }
  
  // Investment Package Methods
  void selectPackage(Map<String, dynamic> package) {
    _currentPackage = package;
    notifyListeners();
  }
  
  void purchasePackage(Map<String, dynamic> package) {
    if (_walletBalance >= package['amount']) {
      _walletBalance -= package['amount'];
      _currentPackage = package;
      notifyListeners();
    }
  }
  
  // Initialize demo data
  void initializeDemoData() {
    _walletBalance = 1500.0;
    _upgradeWallet = 500.0;
    _withdrawalWallet = 300.0;
    _totalEarnings = 2500.0;
    _dailyEarnings = 150.0;
    _referralEarnings = 800.0;
    _levelIncome = 1200.0;
    _totalReferrals = 25;
    _activeReferrals = 18;
    _gamesPlayed = 12;
    _gameEarnings = 600.0;
    _adsWatchedToday = 15;
    _adEarnings = 30.0;
    
    // Demo team members
    _teamMembers = [
      {'name': 'John Doe', 'level': 1, 'earnings': 150.0, 'status': 'Active'},
      {'name': 'Jane Smith', 'level': 1, 'earnings': 200.0, 'status': 'Active'},
      {'name': 'Mike Johnson', 'level': 2, 'earnings': 100.0, 'status': 'Inactive'},
    ];
    
    notifyListeners();
  }
}
