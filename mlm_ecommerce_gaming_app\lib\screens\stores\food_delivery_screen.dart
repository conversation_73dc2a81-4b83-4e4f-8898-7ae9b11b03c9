import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

class FoodDeliveryScreen extends StatefulWidget {
  const FoodDeliveryScreen({super.key});

  @override
  State<FoodDeliveryScreen> createState() => _FoodDeliveryScreenState();
}

class _FoodDeliveryScreenState extends State<FoodDeliveryScreen> {
  final _searchController = TextEditingController();
  String selectedCategory = 'All';

  final List<String> categories = [
    'All',
    'Pizza',
    'Burger',
    'Chinese',
    'Indian',
    'Mexican',
    'Japanese',
    'Desserts',
  ];

  final List<Map<String, dynamic>> restaurants = [
    {
      'name': 'Pizza Palace',
      'image': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      'rating': 4.5,
      'deliveryTime': '30-45 min',
      'category': 'Pizza',
      'offer': '50% OFF',
      'minOrder': '₹199',
      'deliveryFee': '₹29',
      'cuisine': 'Italian, Fast Food',
    },
    {
      'name': 'Burger King',
      'image': 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop',
      'rating': 4.2,
      'deliveryTime': '25-35 min',
      'category': 'Burger',
      'offer': '₹100 OFF',
      'minOrder': '₹149',
      'deliveryFee': 'FREE',
      'cuisine': 'American, Fast Food',
    },
    {
      'name': 'Chinese Corner',
      'image': 'https://images.unsplash.com/photo-1585032226651-759b368d7246?w=400&h=300&fit=crop',
      'rating': 4.3,
      'deliveryTime': '35-50 min',
      'category': 'Chinese',
      'offer': 'Free Delivery',
      'minOrder': '₹299',
      'deliveryFee': 'FREE',
      'cuisine': 'Chinese, Asian',
    },
    {
      'name': 'Spice Garden',
      'image': 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400&h=300&fit=crop',
      'rating': 4.6,
      'deliveryTime': '40-55 min',
      'category': 'Indian',
      'offer': '30% OFF',
      'minOrder': '₹249',
      'deliveryFee': '₹39',
      'cuisine': 'North Indian, Punjabi',
    },
    {
      'name': 'Taco Fiesta',
      'image': 'https://images.unsplash.com/photo-1565299585323-38174c4a6c7b?w=400&h=300&fit=crop',
      'rating': 4.4,
      'deliveryTime': '20-30 min',
      'category': 'Mexican',
      'offer': '40% OFF',
      'minOrder': '₹179',
      'deliveryFee': '₹25',
      'cuisine': 'Mexican, Continental',
    },
    {
      'name': 'Sushi Express',
      'image': 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400&h=300&fit=crop',
      'rating': 4.7,
      'deliveryTime': '45-60 min',
      'category': 'Japanese',
      'offer': 'Buy 1 Get 1',
      'minOrder': '₹399',
      'deliveryFee': '₹49',
      'cuisine': 'Japanese, Sushi',
    },
    {
      'name': 'Dosa Delight',
      'image': 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=400&h=300&fit=crop',
      'rating': 4.5,
      'deliveryTime': '25-40 min',
      'category': 'Indian',
      'offer': '25% OFF',
      'minOrder': '₹159',
      'deliveryFee': '₹19',
      'cuisine': 'South Indian, Breakfast',
    },
    {
      'name': 'Pasta Paradise',
      'image': 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=400&h=300&fit=crop',
      'rating': 4.4,
      'deliveryTime': '30-45 min',
      'category': 'Pizza',
      'offer': '35% OFF',
      'minOrder': '₹229',
      'deliveryFee': '₹35',
      'cuisine': 'Italian, Continental',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Food Delivery'),
        backgroundColor: AppTheme.errorColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          _buildCategories(),
          Expanded(child: _buildRestaurantList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppTheme.errorColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20.r),
          bottomRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.white,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Deliver to: Home, Street 123',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 20.sp,
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Text(
            'What would you like to eat?',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: CustomSearchField(
        controller: _searchController,
        hint: 'Search for restaurants or dishes...',
        onChanged: (value) {
          // TODO: Implement search functionality
        },
      ),
    );
  }

  Widget _buildCategories() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.only(bottom: 20.h),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategory == category;
          
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedCategory = category;
              });
            },
            child: Container(
              margin: EdgeInsets.only(right: 12.w),
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.errorColor : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(25.r),
              ),
              child: Text(
                category,
                style: TextStyle(
                  color: isSelected ? Colors.white : AppTheme.textSecondary,
                  fontSize: 14.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRestaurantList() {
    final filteredRestaurants = selectedCategory == 'All'
        ? restaurants
        : restaurants.where((r) => r['category'] == selectedCategory).toList();

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      itemCount: filteredRestaurants.length,
      itemBuilder: (context, index) {
        final restaurant = filteredRestaurants[index];
        return _buildRestaurantCard(restaurant);
      },
    );
  }

  Widget _buildRestaurantCard(Map<String, dynamic> restaurant) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                height: 150.h,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.r),
                    topRight: Radius.circular(16.r),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.r),
                    topRight: Radius.circular(16.r),
                  ),
                  child: Image.network(
                    restaurant['image'],
                    height: 150.h,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 150.h,
                        width: double.infinity,
                        color: Colors.grey.shade200,
                        child: Icon(
                          Icons.restaurant,
                          size: 50.sp,
                          color: Colors.grey.shade400,
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        height: 150.h,
                        width: double.infinity,
                        color: Colors.grey.shade200,
                        child: Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                top: 12.h,
                left: 12.w,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    restaurant['offer'],
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 12.h,
                right: 12.w,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Icon(
                    Icons.favorite_border,
                    size: 16.sp,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  restaurant['name'],
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  restaurant['cuisine'],
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.textSecondary,
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: AppTheme.warningColor,
                      size: 16.sp,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      restaurant['rating'].toString(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Icon(
                      Icons.access_time,
                      color: AppTheme.textSecondary,
                      size: 16.sp,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      restaurant['deliveryTime'],
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Text(
                      'Min Order: ${restaurant['minOrder']}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Text(
                      'Delivery: ${restaurant['deliveryFee']}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: restaurant['deliveryFee'] == 'FREE'
                            ? AppTheme.successColor
                            : AppTheme.textSecondary,
                        fontWeight: restaurant['deliveryFee'] == 'FREE'
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                CustomButton(
                  text: 'Order Now',
                  onPressed: () {
                    // TODO: Navigate to restaurant menu
                  },
                  height: 36.h,
                  width: double.infinity,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
